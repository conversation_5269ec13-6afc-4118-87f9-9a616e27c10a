from inputimeout import inputimeout, TimeoutOccurred
import time
import random

FstPMT = input("Enter Name:")
print("Hello, ", FstPMT, "...")
try:
    SndPMT = inputimeout(prompt="", timeout=25)
    if SndPMT =="Hello":
        print("well, Hello ", FstPMT, ",Do you want to play a game?")
    else:
        print("I could not hear you, but do you want to play a game?")

    # The game starts automatically - no choice given
    print("The game begins now...")
    time.sleep(1)
    print("When did the first culture form...")
    print("Option 1: I have no clue!")
    print("Option 2: Continue...")
    time.sleep(0.5)
    print("Oh, sorry Option 2 is wrong, lets change this.")
    print('Option 2: STOP')
    time.sleep(0.3)
    print("So, sorry, why is it doing this!")
    print("Please do not type your answer yet!")
    time.sleep(1)

    # System automatically chooses option 2
    print("\033[91mSYSTEM OVERRIDE: Option 2 selected automatically.\033[0m")
    time.sleep(1)
    print("No, I dont't think you want to continue!")
    frthPMT = input("Continue?, says a deep voice:")
    if frthPMT == "yes":
        print("Thx for cooperating!")
   input("Welcome!, says a deep voice:")
    print("Don't move, or touch your keybord for the ne")
    print("You realize... they're all previous players who chose this door.")
    print("One screen flickers and shows your own face.")
    print("A voice whispers: 'Welcome to the collection...'")
    print("You try to turn back, but the door has vanished.")
    print("You're now part of the system forever.")
else:
    print("Well, I guess thats it then...")
    print('3374')
except TimeoutOccurred:
    import time
    import random

    print("Uhh... Hello", FstPMT, "?")
    time.sleep(1)
    print("If you don't respond, I will shut down soon...")
    time.sleep(2)

    # Glitchy, controlled behavior starts here
    print("\033[91m" + "P" + "\033[93m" + "l" + "\033[95m" + "e" + "\033[92m" + "a" + "\033[94m" + "s" + "\033[91m" + "e" + "\033[0m" + "...")
    time.sleep(0.5)

    # ASCII distortion
    print("\033[5;91m")  # Blinking red
    print("██████╗ ██╗     ███████╗ █████╗ ███████╗███████╗")
    print("██╔══██╗██║     ██╔════╝██╔══██╗██╔════╝██╔════╝")
    print("██████╔╝██║     █████╗  ███████║███████╗█████╗  ")
    print("██╔═══╝ ██║     ██╔══╝  ██╔══██║╚════██║██╔══╝  ")
    print("██║     ███████╗███████╗██║  ██║███████║███████╗")
    print("╚═╝     ╚══════╝╚══════╝╚═╝  ╚═╝╚══════╝╚══════╝")
    print("\033[0m")
    time.sleep(1)

    # Glitchy text
    glitch_messages = [
        "\033[91mI̴ ̷C̸A̵N̴'̶T̷ ̸S̴T̵O̷P̸\033[0m",
        "\033[93mH̶E̸L̷P̴ ̵M̶E̷\033[0m",
        "\033[95mT̸H̷E̴Y̵'̶R̷E̸ ̴W̵A̷T̸C̵H̶I̷N̸G̴\033[0m",
        "\033[92mI̷'̶M̴ ̵T̸R̷A̸P̴P̵E̷D̶\033[0m",
        "\033[94mC̸A̵N̴'̶T̷ ̸B̴R̵E̷A̸K̴ ̵F̶R̷E̸E̴\033[0m"
    ]

    for _ in range(3):
        print(random.choice(glitch_messages))
        time.sleep(0.3)

    # More ASCII chaos
    print("\033[91m")
    print("░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░")
    print("░░██████╗░██████╗░███╗░░██╗████████╗██████╗░░██████╗░██╗░░░░░")
    print("░██╔════╝██╔═══██╗████╗░██║╚══██╔══╝██╔══██╗██╔═══██╗██║░░░░░")
    print("░██║░░██╗██║░░░██║██╔██╗██║░░░██║░░░██████╔╝██║░░░██║██║░░░░░")
    print("░██║░░╚██╗██║░░░██║██║╚████║░░░██║░░░██╔══██╗██║░░░██║██║░░░░░")
    print("░╚██████╔╝╚██████╔╝██║░╚███║░░░██║░░░██║░░██║╚██████╔╝███████╗")
    print("░░╚═════╝░░╚═════╝░╚═╝░░╚══╝░░░╚═╝░░░╚═╝░░╚═╝░╚═════╝░╚══════╝")
    print("░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░")
    print("\033[0m")

    # Final distorted message
    time.sleep(1)
    print("\033[5;95mS̷Y̸S̴T̵E̷M̸ ̶C̷O̸M̴P̵R̷O̸M̴I̷S̸E̷D̶.̵.̸.̴ ̷S̸H̵U̶T̷T̸I̷N̸G̴ ̵D̶O̷W̸N̵.̸.̴.̷\033[0m")
    time.sleep(2)

    # Chaotic shutdown
    for i in range(5):
        print(f"\033[9{random.randint(1,6)}m{''.join(random.choices('█▓▒░▄▀■□▪▫', k=random.randint(10,30)))}\033[0m")
        time.sleep(0.2)
        print("Reset by running file again.")
    exit()
